<script setup lang="ts" name="user-view">
import type { ExtendedVxeGridApi } from 'node_modules/@vben/plugins/src/vxe-table/types';

import type { FormType } from '#/adapter/form';

import { useVbenDrawer, useVbenForm } from '@vben/common-ui';
import { formatCascaderValue } from '@vben/utils';

import { message } from 'ant-design-vue';

import { defaultFormConfig } from '#/adapter/form';
import { apixtDwxxbCreate, apixtDwxxbUpdate } from '#/api';

import { createEditFormSchema } from './data';

const form = ref();
type dataType = {
  [key: string]: any;
  FormType: FormType;
  gridApi?: ExtendedVxeGridApi;
  isEdit: boolean;
};
const data = ref<dataType>({
  FormType: 'create',
  isEdit: false,
});
const [Drawer, drawerApi] = useVbenDrawer({
  zIndex: 999,
  class: 'w-2/3',
  onCancel: () => {
    drawerApi.close();
  },
  onConfirm: async () => {
    const values: any = await formApi.validateAndSubmitForm();
    if (!values) return;
    const { gridApi, ...others } = data.value;
    drawerApi.lock();
    drawerApi.setState({ confirmLoading: true });
    await (data.value.isEdit ? apixtDwxxbUpdate : apixtDwxxbCreate)({
      ...others,
      ...values,
      ...formatCascaderValue(values, ['qhdm', 'sjdwdm', 'dwjgdm', 'sjrssxq']),
    })
      .then(() => {
        message.success(data.value.isEdit ? '更新成功' : '创建成功');
        drawerApi.close();
        gridApi?.query();
      })
      .finally(() => {
        drawerApi.unlock();
        drawerApi.setState({ confirmLoading: false });
      });
  },
  async onOpenChange(isOpen: boolean) {
    if (isOpen) {
      data.value = await drawerApi.getData();
      data.value.isEdit = data.value.FormType === 'edit';
      const title = data.value.isEdit ? '编辑' : '新增';
      drawerApi.setState({
        title,
      });
      formApi.setValues(data.value);
      console.log('qhdm value', data.value);
      if (data.value.isEdit) {
        formApi.updateSchema([
          {
            componentProps: {
              params: {
                ssxq: data.value.sjrssxq,
              },
              disabled: true,
              formApi,
            },
            fieldName: 'qhdm',
          },
          {
            componentProps: {
              params: {
                ssxq: data.value.dm,
              },
              disabled: true,
              formApi,
            },
            fieldName: 'dm',
          },
          {
            componentProps: {
              params: {
                ssxq: data.value.sjdwdm,
              },
              formApi,
            },
            fieldName: 'sjdwdm',
          },
          {
            componentProps: {
              params: {
                ssxq: data.value.dwjgdm,
              },
              formApi,
            },
            fieldName: 'dwjgdm',
          },
          {
            componentProps: {
              params: {
                ssxq: data.value.sjrssxq,
              },
              formApi,
            },
            fieldName: 'sjrssxq',
          },
        ]);
      }
    }
  },
});
const [Form, formApi] = useVbenForm({
  // 所有表单项共用，可单独在表单内覆盖
  // 提交函数
  ...defaultFormConfig,
  submitOnEnter: false,
  showDefaultActions: false,
  schema: createEditFormSchema((): any => formApi),
});
</script>
<template>
  <Drawer>
    <Form ref="form" />
  </Drawer>
</template>
<style lang="less" scoped></style>
