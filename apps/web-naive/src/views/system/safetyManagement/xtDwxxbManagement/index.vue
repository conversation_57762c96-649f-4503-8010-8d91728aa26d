<script lang="ts" setup name="user-index">
import type { FormType } from '#/adapter/form';
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { xtDwxxbPageResp } from '#/api';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { formatCascaderValue } from '@vben/utils';

import { message } from 'ant-design-vue';

import { defaultFormConfig } from '#/adapter/form';
import { useFormatterColumns, useVbenVxeGrid } from '#/adapter/vxe-table';
import { apixtDwxxbDelete, apixtDwxxbPage, apixtDwxxbView } from '#/api';

import { QueryFormSchema, useColumns } from './data';
import _Form from './form.vue';
import _View from './view.vue';

const searchCount = ref(true);

const handleView = async (row: xtDwxxbPageResp) => {
  const res = await apixtDwxxbView({ id: row.dm });
  viewApi.setData(res).open();
};
const handleForm = async (row: null | xtDwxxbPageResp, code: FormType) => {
  if (row) {
    const res = await apixtDwxxbView({ id: row!.dm });
    formApi
      .setData({ ...res, qhdm: `${res.qhdm}000`, FormType: code, gridApi })
      .open();
  } else {
    formApi.setData({ FormType: code }).open();
  }
};
const handleDelete = async (row: xtDwxxbPageResp) => {
  await apixtDwxxbDelete([row.dm]);
  message.success('删除成功');
  gridApi.query();
};

const [View, viewApi] = useVbenDrawer({
  connectedComponent: _View,
  destroyOnClose: true,
});
const [Form, formApi] = useVbenDrawer({
  connectedComponent: _Form,
  destroyOnClose: true,
});

const onActionClick = ({ code, row }: OnActionClickParams<xtDwxxbPageResp>) => {
  switch (code) {
    case 'delete': {
      handleDelete(row);
      break;
    }
    case 'edit': {
      handleForm(row, code);
      break;
    }
    case 'view': {
      handleView(row);
      break;
    }
    default: {
      break;
    }
  }
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    schema: QueryFormSchema,
    ...defaultFormConfig,
  },
  gridOptions: {
    columns: useFormatterColumns(useColumns(onActionClick)),
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          const { currentPage, pageSize } = page;
          const result = await apixtDwxxbPage({
            pageNumber: currentPage,
            pageSize,
            ...formValues,
            searchCount: searchCount.value,
            ...formatCascaderValue(formValues, ['dmLike']),
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'zwpy',
    },
  } as VxeTableGridOptions,
});
</script>
<template>
  <Page>
    <Grid>
      <template #toolbar-actions>
        <a-button
          type="primary"
          size="small"
          @click="handleForm(null, 'create')"
        >
          新增
        </a-button>
      </template>
      <template #submit-before>
        <a-switch v-model:checked="searchCount" />
      </template>
    </Grid>
    <View />
    <Form />
  </Page>
</template>
<style lang="scss" scoped></style>
