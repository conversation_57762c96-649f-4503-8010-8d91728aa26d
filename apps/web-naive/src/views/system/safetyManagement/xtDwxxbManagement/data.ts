import type {
  BaseFormComponentType,
  ExtendedFormApi,
  VbenFormSchema,
} from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { z } from '@vben/common-ui';

import pinyin from 'js-pinyin';

pinyin.setOptions({ charCase: 2, checkPolyphone: false });

// 搜索表单配置
export const QueryFormSchema: VbenFormSchema<BaseFormComponentType>[] = [
  {
    component: 'ApiCascader',
    componentProps: {
      params: {
        minLevel: 12,
        dataType: 'ga',
        glmType: 'cxglm',
        ssxq: '',
      },
    },
    fieldName: 'dmLike',
    label: '单位',
  },
  {
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择',
      options: [],
      params: {
        type: 9024,
      },
    },
    fieldName: 'dwjb',
    label: '单位级别',
  },
  {
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择',
      options: [],
      params: {
        type: 9002,
      },
    },
    defaultValue: '1',
    fieldName: 'qybz',
    label: '启用标志',
  },
];

// 表格列数据
export function useColumns<T = any>(
  onActionClick: T,
): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'dm',
      title: '单位代码',
    },
    {
      field: 'mc',
      title: '单位名称',
    },
    {
      field: 'sjdwdm',
      title: '上级单位代码',
    },
    {
      field: 'zwpy',
      title: '中文拼音',
    },
    {
      field: 'dwjgdm',
      title: '单位机构代码',
    },
    {
      field: 'dwjbLabel',
      title: '单位级别',
    },
    {
      field: 'qybzLabel',
      title: '启用标志',
    },
    {
      field: 'fjjgdm',
      title: '分局机构代码',
    },
    {
      field: 'fjjgmc',
      title: '分局机构名称',
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          'view', // 默认的详情按钮
          'edit', // 默认的编辑按钮
          {
            code: 'delete',
            text: '删除',
            show: (row: any) => row.qybz === '1',
          }, // 默认的删除按钮
        ],
      },
      field: 'operation',
      fixed: 'right',
      // showOverflow: false,
      title: '操作',
      minWidth: 160,
    },
  ];
}

let text = '';
// 新增修改表单配置
export const createEditFormSchema = (
  cb: () => ExtendedFormApi,
): VbenFormSchema<BaseFormComponentType>[] => [
  {
    title: '基础信息',
    fieldName: 'titleSlot',
  },
  {
    component: 'ApiCascader',
    componentProps: {
      placeholder: '请选择',
      params: {
        ssxq: '',
        glmType: 'cxglm',
        minLevel: '6',
        dataType: 'zf',
      },
    },
    fieldName: 'qhdm',
    label: '所属区划',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    fieldName: 'dm',
    label: '单位代码',
    help: '9位',
    rules: z.string().length(9, '代码长度为9位'),
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
      onChange: (e: any) => {
        const form = cb();
        form.setFieldValue('zwpy', pinyin.getCamelChars(e.target.value));
      },
    },
    fieldName: 'mc',
    label: '单位名称',
    rules: 'required',
  },
  {
    component: 'ApiCascader',
    componentProps: {
      placeholder: '请选择',
      params: {
        ssxq: '',
        glmType: 'cxglm',
        minLevel: '6',
        dataType: 'ga',
      },
    },
    fieldName: 'sjdwdm',
    label: '所属单位',
    help: '分（市）局代码',
    rules: z.array(z.string()).refine(
      (arr) => {
        const lastElement = arr[arr.length - 1];
        return lastElement?.length === 9;
      },
      {
        message: '代码长度为9位',
      },
    ),
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    fieldName: 'zwpy',
    label: '单位中文拼音',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    fieldName: 'wbpy',
    label: '五笔拼音',
  },
  {
    component: 'ApiCascader',
    componentProps: {
      placeholder: '请选择',
      params: {
        glmType: 'cxglm',
        minLevel: '12',
        dataType: 'dwjg',
      },
    },
    fieldName: 'dwjgdm',
    label: '单位机构',
    help: '12位代码',
    rules: z.array(z.string()).refine(
      (arr) => {
        const lastElement = arr[arr.length - 1];
        return lastElement?.length === 12;
      },
      {
        message: '代码长度为12位',
      },
    ),
  },
  {
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择',
      params: {
        type: '9024',
      },
    },
    fieldName: 'dwjb',
    label: '单位级别',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    fieldName: 'fjjgdm',
    label: '分局机构代码',
    help: '12位代码',
    rules: z.string().length(12, '代码长度为12位'),
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    fieldName: 'fjjgmc',
    label: '分局机构名称',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    fieldName: 'gddh',
    label: '单位固定电话',
    rules: 'required',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    fieldName: 'bz',
    label: '备注',
  },
  {
    title: '快递收件地址信息',
    fieldName: 'titleSlot',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    fieldName: 'sjrxm',
    label: '收件人姓名',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    fieldName: 'sjrlxdh',
    label: '收件电话',
  },
  {
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    fieldName: 'sjyb',
    label: '收件人邮编',
  },
  {
    component: 'ApiCascader',
    componentProps: {
      placeholder: '请选择',
      multiplie: false,
      checkStrategy: 'child',
      allowCheckingNotLoaded: true,
      params: {
        glmType: 'none',
        minLevel: '6',
        dataType: 'zf',
        ssxq: '',
      },
      onChange: async (value: any, selectedOptions: any) => {
        const form = cb();
        form.getValues().then(({ sjrxz }) => {
          sjrxz = sjrxz || '';
          form.setFieldValue('sjrssxq', value);
          text = selectedOptions[selectedOptions.length - 1].text;
          form.setFieldValue('sjdz', text + sjrxz);
        });
      },
    },
    fieldName: 'sjrssxq',
    label: '收件人省市县区',
  },
  {
    component: 'Textarea',
    componentProps: {
      placeholder: '请输入',
      onChange: async (e: any) => {
        const form = cb();
        form.setFieldValue('sjdz', text + e.target.value);
      },
    },
    fullWidth: true,
    fieldName: 'sjrxz',
    label: '收件人详址',
  },
  {
    component: 'Textarea',
    componentProps: {
      placeholder: '请输入',
      disabled: true,
    },
    fullWidth: true,
    fieldName: 'sjdz',
    label: '收件人地址',
    help: '完整通讯录地址',
  },
];
